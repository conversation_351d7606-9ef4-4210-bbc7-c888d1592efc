# 产品需求文档 (PRD): Whisper-Transcriber

## 1. 产品名称
- Whisper-Transcriber (或 “耳语速记”)

## 2. 产品简介 (Vision)
一款简洁、高效的桌面应用程序，利用 OpenAI Whisper 强大的语音识别能力，帮助用户快速、准确地将本地的音频和视频文件转换为文本文档或字幕文件。它旨在为内容创作者、记者、学生和任何需要处理音视频转录的用户提供便利。

## 3. 目标用户 (Target Audience)
- **视频博主/播客主：** 快速为视频/音频内容生成字幕和文稿。
- **记者/编辑：** 整理采访录音，提高工作效率。
- **学生/研究人员：** 记录课程、讲座和会议内容，方便后续查阅和分析。
- **普通用户：** 任何有临时性音视频转录需求的用户。

## 4. 核心功能 (Features)

### 功能1：核心转录引擎
- **描述：** 程序内嵌 OpenAI Whisper 模型，作为核心的语音转文字引擎。
- **要求：**
  - 支持多种常见的音频格式（如 `.mp3`, `.wav`, `.m4a`）。
  - 支持多种常见的视频格式（如 `.mp4`, `.mov`, `.avi`），程序能自动提取其中的音频流进行处理。
  - 用户可以选择不同的 Whisper 模型（如 `tiny`, `base`, `small`, `medium`, `large`），以在速度和精度之间做出权衡。

### 功能2：用户界面 (UI)
- **描述：** 提供一个直观、易于操作的图形界面。
- **要求：**
  - 界面布局清晰，主要功能区一目了然。
  - 包含文件/文件夹选择按钮。
  - 包含模型选择下拉菜单。
  - 包含“开始转换”按钮。
  - 提供一个进度条或状态显示，实时反馈转换进度。
  - 提供一个文本框或区域，用于显示转换完成后的结果。

### 功能3：文件处理模式
- **描述：** 支持两种文件处理模式，满足不同场景的需求。
- **要求：**
  - **单文件处理：** 用户可以选择一个单独的音视频文件进行转换。
  - **批量处理：** 用户可以选择一个文件夹，程序会自动遍历文件夹内所有支持的音视频文件，并逐一进行转换。

### 功能4：输出与保存
- **描述：** 用户可以将转换结果保存到本地。
- **要求：**
  - 支持将结果保存为纯文本文件 (`.txt`)。
  - 支持将结果保存为字幕文件 (`.srt`)，包含时间戳。
  - 批量处理时，生成的文本文件应与原音视频文件同名（仅扩展名不同），并保存在指定的输出目录中。

## 5. 未来规划 (Roadmap)
- 支持多语言识别。
- 增加“说话人分离”功能（Diarization）。
- 提供简单的文本编辑功能，方便用户在程序内校对和修改结果。
- GPU加速选项，为有兼容硬件的用户提供更快的转换速度。

## 6. 技术实现方案

### 核心库
- **`openai-whisper`**: 实现所有转录功能的核心。
- **`ffmpeg`**: Whisper 依赖 `ffmpeg` 来处理视频文件和进行音频格式转换。
- **`gradio`**: 用于快速构建Web UI。

### 程序结构
- **`app.py` (主程序文件):**
  - 导入 `whisper` 和 `gradio`。
  - 加载 Whisper 模型。
  - 定义核心的转录函数。
  - 使用 `gradio.Blocks()` 来构建UI布局。
  - 启动 Gradio 服务。
