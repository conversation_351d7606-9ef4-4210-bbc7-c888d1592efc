#!/usr/bin/env python3
"""
简单测试脚本，验证 SRT 时间戳格式化是否正常工作
"""

def format_timestamp(seconds):
    """将秒转换为SRT时间戳格式 (HH:MM:SS,ms)"""
    # 修复弃用警告，使用更现代的方法
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}"

# 测试时间戳格式化
test_times = [0.0, 7.28, 13.36, 65.5, 3661.123]

print("测试 SRT 时间戳格式化:")
for t in test_times:
    formatted = format_timestamp(t)
    print(f"{t:8.3f} 秒 -> {formatted}")

# 模拟 SRT 内容生成
print("\n模拟 SRT 内容生成:")
segments = [
    {'start': 0.0, 'end': 7.28, 'text': ' You found your keywords, you\'ve created your content.'},
    {'start': 7.28, 'end': 13.36, 'text': ' So now you\'re going to enter what is possibly the hardest part.'},
    {'start': 13.36, 'end': 14.36, 'text': ' Patience.'}
]

srt_content = ""
for i, segment in enumerate(segments):
    start_time = format_timestamp(segment['start'])
    end_time = format_timestamp(segment['end'])
    srt_content += f"{i + 1}\n"
    srt_content += f"{start_time} --> {end_time}\n"
    srt_content += f"{segment['text'].strip()}\n\n"

print(srt_content)
