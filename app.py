import gradio as gr
import whisper
import os
import tempfile
from datetime import datetime

# 加载模型
# 为了快速启动，我们可以先加载一个较小的模型，或者让用户选择后加载
# model = whisper.load_model("base") 

def transcribe_audio(file_path, model_size, output_format):
    """
    使用Whisper转录音频文件。
    :param file_path: 音频/视频文件的路径。
    :param model_size: 要使用的Whisper模型大小 (e.g., "tiny", "base", "small", "medium", "large")。
    :param output_format: 输出格式 ("txt" or "srt")。
    :return: 转录后的文本或字幕内容。
    """
    try:
        print(f"正在加载模型: {model_size}...")
        model = whisper.load_model(model_size)
        print("模型加载完毕。")
        
        print(f"开始转录文件: {file_path}...")
        # 使用verbose=True可以在后台看到详细的转录过程
        result = model.transcribe(file_path, verbose=True)
        print("转录完成。")

        if output_format == "txt":
            return result["text"]
        elif output_format == "srt":
            # 创建SRT格式内容
            srt_content = ""
            for i, segment in enumerate(result["segments"]):
                start_time = format_timestamp(segment['start'])
                end_time = format_timestamp(segment['end'])
                srt_content += f"{i + 1}\n"
                srt_content += f"{start_time} --> {end_time}\n"
                srt_content += f"{segment['text'].strip()}\n\n"
            return srt_content

    except Exception as e:
        return f"发生错误: {e}"

def format_timestamp(seconds):
    """将秒转换为SRT时间戳格式 (HH:MM:SS,ms)"""
    # 修复弃用警告，使用更现代的方法
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}"

def single_file_interface(filepath, model_size, output_format):
    """Gradio的单文件处理接口"""
    if filepath is None:
        return "请先上传一个文件。", ""

    print(f"接收到文件路径: {filepath}")

    # 直接使用Gradio提供的临时文件路径
    transcribed_text = transcribe_audio(filepath, model_size, output_format)

    # 创建可下载的文件，使用原始文件名
    original_filename = os.path.splitext(os.path.basename(filepath))[0]
    output_filename = f"{original_filename}.{output_format}"

    # 将结果写入临时文件以便下载，使用原始文件名作为前缀
    temp_dir = tempfile.gettempdir()
    download_path = os.path.join(temp_dir, output_filename)

    with open(download_path, "w", encoding="utf-8") as f:
        f.write(transcribed_text)

    return transcribed_text, download_path


def batch_files_interface(filepaths, model_size, output_format):
    """Gradio的批量文件处理接口"""
    if not filepaths:
        return "请先上传文件。", None

    # 创建一个临时目录来存放所有结果
    temp_dir = tempfile.mkdtemp()

    all_results_text = f"开始批量处理 {len(filepaths)} 个文件...\n"
    all_results_text += f"输出格式: {output_format.upper()}\n"
    all_results_text += f"模型: {model_size}\n\n"

    for i, filepath in enumerate(filepaths, 1):
        filename = os.path.basename(filepath)
        all_results_text += f"[{i}/{len(filepaths)}] 正在处理: {filename}\n"

        transcribed_text = transcribe_audio(filepath, model_size, output_format)

        # 检查是否有错误
        if transcribed_text.startswith("发生错误:"):
            all_results_text += f"❌ 处理失败: {transcribed_text}\n\n"
            continue

        # 将单个结果保存到临时目录
        output_path = os.path.join(temp_dir, f"{os.path.splitext(filename)[0]}.{output_format}")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(transcribed_text)

        # 显示处理结果摘要
        if output_format == "srt":
            lines = transcribed_text.count('\n')
            all_results_text += f"✅ 已生成 SRT 字幕文件 ({lines} 行)\n"
        else:
            words = len(transcribed_text.split())
            all_results_text += f"✅ 已生成文本文件 ({words} 词)\n"

        all_results_text += f"保存为: {os.path.splitext(filename)[0]}.{output_format}\n\n"

    # 将所有结果打包成一个zip文件，使用时间戳作为文件名
    all_results_text += "正在打包所有文件...\n"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"transcribed_files_{timestamp}"
    zip_path = os.path.join(tempfile.gettempdir(), zip_filename)
    import shutil
    shutil.make_archive(zip_path, 'zip', temp_dir)

    all_results_text += f"✅ 批量处理完成！\n"
    all_results_text += f"所有文件已打包为: {zip_filename}.zip\n"

    return all_results_text, f"{zip_path}.zip"


# --- Gradio UI ---
with gr.Blocks() as demo:
    gr.Markdown("# Whisper Transcriber (耳语速记)")
    gr.Markdown("上传音频/视频文件，选择模型和输出格式，然后点击“开始转换”。")

    model_options = ["tiny", "base", "small", "medium", "large"]
    output_options = ["txt", "srt"]

    with gr.Tab("单文件转换"):
        with gr.Row():
            with gr.Column():
                file_input = gr.File(label="上传单个文件 (音频或视频)", type="filepath")
                model_select_single = gr.Dropdown(model_options, label="选择模型", value="base")
                output_format_single = gr.Radio(output_options, label="输出格式", value="txt")
                start_button_single = gr.Button("开始转换")
            with gr.Column():
                text_output_single = gr.Textbox(label="转换结果", lines=15)
                file_download_single = gr.File(label="下载结果文件")
        
        start_button_single.click(
            single_file_interface,
            inputs=[file_input, model_select_single, output_format_single],
            outputs=[text_output_single, file_download_single]
        )

    with gr.Tab("批量转换"):
        with gr.Row():
            with gr.Column():
                files_input = gr.File(label="上传多个文件", file_count="multiple", type="filepath")
                model_select_batch = gr.Dropdown(model_options, label="选择模型", value="base")
                output_format_batch = gr.Radio(output_options, label="输出格式", value="txt")
                start_button_batch = gr.Button("开始批量转换")
            with gr.Column():
                text_output_batch = gr.Textbox(label="所有文件转换结果汇总", lines=15)
                zip_download_batch = gr.File(label="下载所有结果 (ZIP)")

        start_button_batch.click(
            batch_files_interface,
            inputs=[files_input, model_select_batch, output_format_batch],
            outputs=[text_output_batch, zip_download_batch]
        )

if __name__ == "__main__":
    demo.launch()
